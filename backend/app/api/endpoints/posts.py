"""
API endpoints for post generation using multiple AI providers.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
import google.generativeai as genai

from app.services.meme_service import MemeService
from app.services.ideogram_service import IdeogramService
from app.services.watermark_service import watermark_service

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize services
meme_service = MemeService()
ideogram_service = IdeogramService()


class PostGenerationRequest(BaseModel):
    """Request schema for post generation."""
    # Brand information from steps 1-3
    brandInfo: Dict[str, Any] = Field(..., description="Complete brand information")
    
    # Design configuration from step 4
    designConfig: Dict[str, Any] = Field(..., description="Design and template configuration")
    
    # Generation configuration
    generationConfig: Dict[str, Any] = Field(default_factory=dict, description="Generation settings")


class GeneratedPost(BaseModel):
    """Schema for a single generated post."""
    id: str = Field(..., description="Unique post ID")
    text: str = Field(..., description="Generated text content")
    image_url: Optional[str] = Field(None, description="Generated image URL")
    template: str = Field(..., description="Template used")
    platform: str = Field(..., description="Target platform")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class PostGenerationResponse(BaseModel):
    """Response schema for post generation."""
    success: bool = Field(..., description="Whether generation was successful")
    posts: List[GeneratedPost] = Field(default_factory=list, description="Generated posts")
    total_generated: int = Field(0, description="Total number of posts generated")
    error: Optional[str] = Field(None, description="Error message if generation failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Generation metadata")


def get_platform_dimensions(platform: str, content_type: str) -> str:
    """
    Get platform-specific image dimensions.

    Args:
        platform: Target platform (Instagram, Facebook, LinkedIn, etc.)
        content_type: Type of content (posts, stories, etc.)

    Returns:
        Dimension string in format "WIDTHxHEIGHT"
    """
    platform_dimensions = {
        "Instagram": {
            "instagram_posts": "1080x1080",  # Square posts
            "instagram_stories": "1080x1920",  # Vertical stories
            "default": "1080x1080"
        },
        "Facebook": {
            "facebook_posts": "1200x630",  # Landscape posts
            "default": "1200x630"
        },
        "LinkedIn": {
            "linkedin_posts": "1200x627",  # Professional posts
            "default": "1200x627"
        },
        "X (Twitter)": {
            "twitter_posts": "1200x675",  # Twitter posts
            "default": "1200x675"
        }
    }

    platform_config = platform_dimensions.get(platform, platform_dimensions["Instagram"])
    return platform_config.get(content_type, platform_config["default"])


def select_image_provider(template: str, content_description: str) -> str:
    """
    Intelligently select image provider based on template and content.

    PRIORITY ORDER:
    1. LOCAL MEME: Template-based memes (cost-effective)
    2. IDEOGRAM 3.0 QUALITY: All other content types (PRIMARY)

    Args:
        template: The selected template name
        content_description: Description of the content to generate

    Returns:
        Provider name: 'meme' or 'ideogram'
    """
    content_lower = content_description.lower()

    # LOCAL MEME service for template-based memes (cost optimization)
    if template == "Meme" or any(keyword in content_lower for keyword in [
        "meme", "humor", "gracioso", "divertido", "vs", "antes", "después", "template"
    ]):
        return "meme"

    # IDEOGRAM 3.0 QUALITY for all other content (text-heavy, visual, professional)
    # This includes: quotes, comics, landscapes, products, scenes, graphics, professional photography
    return "ideogram"


def generate_strategic_content_plan(template: str, brand_info: Dict[str, Any], platform: str) -> Dict[str, str]:
    """
    Generate strategic content plan with separated visual and textual content.

    Args:
        template: Template name
        brand_info: Brand information
        platform: Target platform

    Returns:
        Dict with visual_hook, post_content, and strategy
    """
    business_name = brand_info.get("businessName", "Business")
    industry = brand_info.get("industry", "business")
    target_audience = brand_info.get("target_audience", "professionals")
    voice = brand_info.get("voice", "professional")

    # Strategic content templates with separated visual and textual content
    content_strategies = {
        # PROFESSIONAL CONTENT STRATEGIES
        "Balance": {
            "visual_hook": f"Key insight or statistic about {industry} success",
            "post_strategy": f"Professional analysis and actionable advice for {target_audience} in {industry}",
            "content_type": "educational_professional"
        },

        "Educational": {
            "visual_hook": f"Step-by-step process or framework for {industry}",
            "post_strategy": f"Detailed explanation with real examples and implementation tips for {business_name} audience",
            "content_type": "tutorial_educational"
        },

        "Motivational": {
            "visual_hook": f"Inspiring quote or success metric from {industry}",
            "post_strategy": f"Personal story or case study that motivates {target_audience} to take action",
            "content_type": "inspirational_story"
        },

        "Informativo": {
            "visual_hook": f"Breaking news or trend data in {industry}",
            "post_strategy": f"Analysis of what this means for {business_name} clients and actionable next steps",
            "content_type": "news_analysis"
        },

        # ENGAGEMENT CONTENT STRATEGIES
        "Storytelling": {
            "visual_hook": f"Compelling moment or transformation in {business_name} journey",
            "post_strategy": f"Complete narrative with lessons learned and value for {target_audience}",
            "content_type": "narrative_case_study"
        },

        "Behind_Scenes": {
            "visual_hook": f"Authentic moment from {business_name} daily operations",
            "post_strategy": f"Transparent insight into processes with educational value for audience",
            "content_type": "transparency_education"
        },

        "Controversial": {
            "visual_hook": f"Thought-provoking question or contrarian viewpoint about {industry}",
            "post_strategy": f"Balanced argument with evidence and invitation for professional discussion",
            "content_type": "thought_leadership"
        },

        # VIRAL CONTENT STRATEGIES
        "Viral": {
            "visual_hook": f"Surprising statistic or counterintuitive fact about {industry}",
            "post_strategy": f"Explanation of why this matters and how {target_audience} can benefit",
            "content_type": "insight_revelation"
        },

        "Trending": {
            "visual_hook": f"Current trend or event relevant to {industry}",
            "post_strategy": f"Professional take on how this affects {business_name} clients with actionable advice",
            "content_type": "trend_analysis"
        }
    }

    # Platform-specific adaptations
    platform_adaptations = {
        "Instagram": {
            "visual_focus": "Strong visual impact with minimal text",
            "post_focus": "Engaging storytelling with relevant hashtags",
            "tone": "Visual-first, authentic, community-focused"
        },
        "LinkedIn": {
            "visual_focus": "Professional data or insights",
            "post_focus": "Industry expertise and networking value",
            "tone": "Professional, authoritative, business-focused"
        },
        "Facebook": {
            "visual_focus": "Community-relevant content",
            "post_focus": "Conversation-starting with detailed context",
            "tone": "Conversational, community-building, accessible"
        },
        "X": {
            "visual_focus": "Concise, impactful statement",
            "post_focus": "Thread-worthy insights with shareability",
            "tone": "Concise, timely, discussion-provoking"
        }
    }

    strategy = content_strategies.get(template, content_strategies["Balance"])
    platform_adaptation = platform_adaptations.get(platform, platform_adaptations["Instagram"])

    return {
        "visual_hook": strategy["visual_hook"],
        "post_strategy": strategy["post_strategy"],
        "content_type": strategy["content_type"],
        "platform_adaptation": platform_adaptation,
        "brand_context": {
            "business_name": business_name,
            "industry": industry,
            "target_audience": target_audience,
            "voice": voice
        }
    }


def convert_hex_to_color_description(hex_color: str) -> str:
    """
    Convert hex color code to descriptive color name for Ideogram prompts.
    Ideogram works better with descriptive color names than hex codes.

    Args:
        hex_color: Hex color code (e.g., "#3018ef")

    Returns:
        Descriptive color name for prompts
    """
    if not hex_color or not hex_color.startswith('#'):
        return "professional blue"

    # Remove # and convert to lowercase
    hex_clean = hex_color[1:].lower()

    # Color mapping based on common brand colors
    color_mappings = {
        # Blues
        "3018ef": "vibrant electric blue",
        "0066cc": "corporate blue",
        "1e90ff": "bright blue",
        "4169e1": "royal blue",
        "0000ff": "pure blue",
        "87ceeb": "sky blue",
        "add8e6": "light blue",

        # Reds/Pinks
        "dd3a5a": "vibrant pink-red",
        "ff0000": "bright red",
        "dc143c": "crimson red",
        "ff1493": "deep pink",
        "ff69b4": "hot pink",
        "ffc0cb": "soft pink",

        # Greens
        "00ff00": "bright green",
        "32cd32": "lime green",
        "228b22": "forest green",
        "90ee90": "light green",
        "00fa9a": "medium spring green",

        # Purples
        "800080": "purple",
        "9932cc": "dark orchid",
        "ba55d3": "medium orchid",
        "dda0dd": "plum",

        # Oranges/Yellows
        "ffa500": "orange",
        "ff8c00": "dark orange",
        "ffff00": "yellow",
        "ffd700": "gold",
        "ffa07a": "light salmon",

        # Grays/Blacks
        "000000": "black",
        "808080": "gray",
        "c0c0c0": "silver",
        "ffffff": "white",
        "2f2f2f": "dark gray",
    }

    # Check for exact match first
    if hex_clean in color_mappings:
        return color_mappings[hex_clean]

    # Analyze RGB components for general color family
    try:
        r = int(hex_clean[0:2], 16)
        g = int(hex_clean[2:4], 16)
        b = int(hex_clean[4:6], 16)

        # Determine dominant color
        if r > g and r > b:
            if r > 200:
                return "bright red"
            elif r > 150:
                return "vibrant red"
            else:
                return "deep red"
        elif g > r and g > b:
            if g > 200:
                return "bright green"
            elif g > 150:
                return "vibrant green"
            else:
                return "deep green"
        elif b > r and b > g:
            if b > 200:
                return "bright blue"
            elif b > 150:
                return "vibrant blue"
            else:
                return "deep blue"
        else:
            # Mixed colors
            if r > 150 and g > 150:
                return "warm yellow"
            elif r > 150 and b > 150:
                return "vibrant purple"
            elif g > 150 and b > 150:
                return "teal blue"
            else:
                return "neutral gray"

    except ValueError:
        return "professional blue"


def generate_visual_hook_content(content_strategy: Dict[str, Any], brand_info: Dict[str, Any]) -> str:
    """
    Generate compelling visual hook content for images based on strategic content plan.
    Creates text that will appear IN the image - should be concise and impactful.

    Args:
        content_strategy: Strategic content plan from generate_strategic_content_plan
        brand_info: Brand information

    Returns:
        Text content for the image (hook, statistic, or key message)
    """
    business_name = brand_info.get("businessName", "Business")
    industry = brand_info.get("industry", "business")
    content_type = content_strategy.get("content_type", "educational_professional")

    # Generate specific visual hooks based on content type and industry - IN SPANISH
    visual_hooks = {
        "educational_professional": [
            f"3 Estrategias Clave que Transforman Resultados en {industry.title()}",
            f"El Framework de {industry.title()} que Realmente Funciona",
            f"Lo que 90% de Profesionales en {industry.title()} Hacen Mal"
        ],
        "tutorial_educational": [
            f"Paso a Paso: Domina {industry.title()} en 2024",
            f"La Lista Completa de {industry.title()}",
            f"De Principiante a Experto: Ruta de {industry.title()}"
        ],
        "inspirational_story": [
            f"De Cero al Éxito: Un Viaje en {industry.title()}",
            f"Por Qué Inicié {business_name}: La Historia Real",
            f"El Momento que Todo Cambió en {industry.title()}"
        ],
        "news_analysis": [
            f"Último Momento: Cambios en la Industria de {industry.title()} 2024",
            f"Nuevos Datos Revelan Tendencias de {industry.title()}",
            f"Lo que Esto Significa para Profesionales de {industry.title()}"
        ],
        "narrative_case_study": [
            f"Éxito del Cliente: Transformación en {industry.title()}",
            f"Detrás de Escena: Cómo Resolvimos este Desafío de {industry.title()}",
            f"Resultados Reales: Caso de Estudio de {business_name}"
        ],
        "transparency_education": [
            f"Dentro de {business_name}: Cómo Trabajamos Realmente",
            f"La Verdad sobre {industry.title()} que Nadie Habla",
            f"Mirada Honesta: Nuestro Proceso de {industry.title()}"
        ],
        "thought_leadership": [
            f"Opinión Impopular: {industry.title()} Necesita Cambiar",
            f"Por Qué Todos Están Equivocados sobre {industry.title()}",
            f"El Mito de {industry.title()} que Te Está Frenando"
        ],
        "insight_revelation": [
            f"73% de Profesionales en {industry.title()} No Saben Esto",
            f"La Verdad Oculta sobre el Éxito en {industry.title()}",
            f"Lo que los Datos de {industry.title()} Realmente Muestran"
        ],
        "trend_analysis": [
            f"Tendencias 2024 de {industry.title()} que No Puedes Ignorar",
            f"Cómo Esta Tendencia Cambiará {industry.title()} Para Siempre",
            f"El Futuro de {industry.title()} Está Aquí"
        ]
    }

    # Select appropriate hook based on content type
    hooks = visual_hooks.get(content_type, visual_hooks["educational_professional"])
    import random
    selected_hook = random.choice(hooks)

    return selected_hook


def generate_image_prompt_strategic(visual_hook: str, content_strategy: Dict[str, Any], platform: str, brand_color: str = None) -> str:
    """
    Generate strategic image prompt following Ideogram best practices.
    Creates professional, brand-appropriate visuals.

    CRITICAL RULES:
    - All prompts in English
    - Text for images in quotes
    - Stylized/cartoon/illustrated humans OK, NO realistic/photographic humans
    - Focus on professional design elements

    Args:
        visual_hook: The text that will appear in the image (will be quoted)
        content_strategy: Strategic content plan
        platform: Target platform
        brand_color: Brand color (hex code like #3018ef)

    Returns:
        Complete prompt for image generation (English with quoted text)
    """
    brand_context = content_strategy.get("brand_context", {})
    industry = brand_context.get("industry", "business")
    content_type = content_strategy.get("content_type", "educational_professional")

    # Convert hex color to descriptive color for Ideogram
    brand_color_description = convert_hex_to_color_description(brand_color) if brand_color else "professional blue"

    # Professional visual styles - stylized/cartoon humans OK, NO realistic humans
    visual_styles = {
        "educational_professional": f"Clean professional infographic layout with modern typography and data visualization icons. Corporate {industry} aesthetic with geometric elements, charts, and stylized business characters if needed.",
        "tutorial_educational": f"Step-by-step visual guide with numbered icons and clear hierarchy. Educational design with {industry} symbols, professional styling, and cartoon-style instructional characters if helpful.",
        "inspirational_story": f"Success visualization with upward arrows and achievement symbols. Growth-oriented imagery with {industry} professional icons and stylized success characters if appropriate.",
        "news_analysis": f"News-style graphic with bold typography and data visualization elements. Professional journalism aesthetic with {industry} industry symbols and illustrated news characters if relevant.",
        "narrative_case_study": f"Case study presentation with before/after comparison charts. Professional {industry} documentation with infographic elements and stylized case study characters if needed.",
        "transparency_education": f"Clean process flow diagram with transparent design elements. Honest visual approach with {industry} operational icons and cartoon-style workplace characters if appropriate.",
        "thought_leadership": f"Bold authoritative design with strong typography and leadership symbols. Thought-provoking visual style with professional {industry} elements and stylized leadership characters if relevant.",
        "insight_revelation": f"Data-driven infographic with statistics and surprising insights visualization. Eye-catching design highlighting key {industry} metrics with illustrated data characters if helpful.",
        "trend_analysis": f"Future-focused design with trending visual elements and innovation symbols. Modern forward-thinking aesthetic with {industry} technology themes and stylized innovation characters if appropriate."
    }

    base_style = visual_styles.get(content_type, visual_styles["educational_professional"])

    # Platform-specific optimizations (no humans)
    platform_specs = {
        "Instagram": "square format composition, Instagram-optimized color palette, social media native graphic design",
        "LinkedIn": "professional business layout, corporate color scheme, LinkedIn-appropriate business graphics",
        "Facebook": "engaging social media design, community-friendly graphics, Facebook-optimized visual layout",
        "X": "concise visual impact design, Twitter-style brevity, shareable graphic elements"
    }

    platform_spec = platform_specs.get(platform, platform_specs["Instagram"])

    # Construct Ideogram-optimized prompt (English with quoted text + brand color)
    complete_prompt = f"""Professional social media graphic design with the text "{visual_hook}" prominently displayed as the main headline.

Visual Style: {base_style}

Platform Requirements: {platform_spec}

Brand Color Integration: Primary brand color is {brand_color_description}, use this color prominently in the design elements, typography accents, background gradients, or geometric shapes. The brand color should be the dominant accent color throughout the composition.

Text Integration: The text "{visual_hook}" should be the central focus, clearly readable with modern typography, professionally integrated into the graphic design layout. Consider using the brand color ({brand_color_description}) for text highlights or background elements.

Design Requirements: Professional graphic design, stylized/cartoon/illustrated characters allowed (NO realistic or photographic humans), geometric elements, professional color scheme featuring {brand_color_description}, clean modern aesthetic, business-appropriate visual elements.

Quality: High-quality professional graphic design suitable for business marketing, clean composition, strong visual hierarchy, consistent brand color usage."""

    return complete_prompt


def optimize_text_for_ideogram(text: str) -> str:
    """
    Optimize text for Ideogram based on REAL testing and limits discovered.

    Key findings from testing:
    - ✅ Short text (1-4 words): PERFECT
    - ✅ Medium text (5-8 words): GOOD
    - ⚠️ Long text (9+ words): STARTS TO FAIL
    - ❌ Very long text (15+ words): FAILS BADLY

    Args:
        text: Original post text

    Returns:
        Optimized text for Ideogram (max 8 words)
    """
    # Remove emojis and hashtags first
    clean_text = text
    emojis_to_remove = ['🎯', '💡', '📊', '🔥', '💀', '📖', '💣', '🎭', '✨', '🚀', '💪', '⭐', '😎', '🤯', '😅', '📚', '🎓', '🔍', '📰', '📈', '🔔']
    for emoji in emojis_to_remove:
        clean_text = clean_text.replace(emoji, '')

    # Remove hashtags and extra spaces
    clean_text = clean_text.replace('#', '').strip()
    clean_text = ' '.join(clean_text.split())  # Remove extra spaces

    # Shorten common long words for better rendering
    word_replacements = {
        'innovación': 'innovar',
        'tradicional': 'clásico',
        'estrategia': 'plan',
        'consistencia': 'constancia',
        'emprendimiento': 'negocio',
        'metodologías': 'métodos',
        'funcionalidades': 'funciones',
        'actualización': 'update',
        'perspectiva': 'visión',
        'completamente': 'total',
        'perfectamente': 'perfecto',
        'absolutamente': 'total',
        'definitivamente': 'seguro'
    }

    for long_word, short_word in word_replacements.items():
        clean_text = clean_text.replace(long_word, short_word)

    # CRITICAL: Limit to max 8 words based on testing
    words = clean_text.split()
    if len(words) > 8:
        # Try to keep the most important part
        if 'Emma Studio' in clean_text:
            # Keep Emma Studio + 6 more words max
            emma_index = None
            for i, word in enumerate(words):
                if 'Emma' in word:
                    emma_index = i
                    break

            if emma_index is not None:
                # Take Emma Studio + up to 6 more words
                start_idx = emma_index
                end_idx = min(start_idx + 8, len(words))
                words = words[start_idx:end_idx]
            else:
                # Just take first 8 words
                words = words[:8]
        else:
            # Take first 8 words
            words = words[:8]

    final_text = ' '.join(words)

    # Final length check - if still too long, truncate
    if len(final_text) > 50:  # Character limit based on testing
        final_text = final_text[:47] + '...'

    return final_text.strip()


def select_consistent_style_for_batch(template: str, brand_color: str = None) -> str:
    """
    Select ONE consistent visual style for the entire batch generation.
    This ensures all posts in a single generation look like they were made by the same designer.

    CRITICAL: Stylized/cartoon/illustrated humans are OK, but NEVER realistic or photographic humans.
    Focus on professional designs with stylized elements when appropriate.

    Args:
        template: Content template type
        brand_color: Brand color (hex code like #3018ef)

    Returns:
        Selected style prompt for the entire batch (English with quoted Spanish text)
    """
    # Convert brand color to description for prompts
    brand_color_description = convert_hex_to_color_description(brand_color) if brand_color else "professional blue"
    # PROFESSIONAL PROMPTS - English prompts with Spanish text in quotes
    # Key: Professional design + stylized elements (comic/illustrated humans OK, NO realistic humans) + quoted text
    detailed_prompts = {
        # VIRAL CONTENT - Professional designs with stylized elements
        "Viral": [
            f"Modern abstract geometric composition with dynamic arrows and digital elements representing business growth, vibrant gradient featuring {brand_color_description} as primary color, with the text \"{{text}}\" in bold contemporary typography, clean minimalist background, professional graphic design aesthetic",
            f"Sleek infographic-style layout with contrasting data visualization elements, color blocks featuring {brand_color_description} prominently arranged in modern grid pattern, with the text \"{{text}}\" as prominent headline, flat design style with stylized cartoon business icons",
            f"Abstract tech-inspired design with geometric shapes and circuit-like patterns, gradient featuring {brand_color_description} as dominant color, with the text \"{{text}}\" integrated into the design structure, futuristic and professional aesthetic with comic-style innovation symbols"
        ],
        "Storytelling": [
            "Timeline infographic design with connected milestone icons and progress indicators, warm color palette (gold, navy, white) with elegant typography, with the text \"{text}\" as central narrative element, sophisticated business aesthetic",
            "Abstract journey visualization using flowing lines and achievement symbols, earthy professional colors with organic textures, with the text \"{text}\" in elegant serif font, premium brand aesthetic",
            "Process flow diagram with interconnected elements and growth arrows, progressive color scheme from muted to vibrant tones, with the text \"{text}\" marking key stages, inspiring professional design"
        ],
        "Controversial": [
            "Bold contrast design with opposing geometric elements (traditional vs modern symbols), high-contrast color scheme (black, white, red accents), with the text \"{text}\" in striking bold typography, provocative yet professional aesthetic",
            "Split-screen comparison layout with contrasting visual styles and color palettes, with the text \"{text}\" as central dividing element, edgy modern design approach",
            "Abstract disruption visualization with clashing geometric patterns and bold typography, dynamic composition with the text \"{text}\" as focal point, thought-provoking professional design"
        ],
        "Behind_Scenes": [
            "Clean workspace visualization with organized desk elements and productivity icons, trustworthy color palette (blues, whites, grays), with the text \"{text}\" integrated naturally, transparent professional aesthetic",
            "Process transparency infographic with step-by-step visual elements and clear hierarchy, authentic color scheme with the text \"{text}\" as honest insight, approachable business design",
            "Operational flow chart with interconnected system elements and clear pathways, genuine everyday colors with the text \"{text}\" revealing key information, relatable professional aesthetic"
        ],
        "Trending": [
            "Cutting-edge abstract design with neon accent colors and ultra-modern geometric patterns, contemporary typography with the text \"{text}\" in trendy style, innovative forward-looking aesthetic",
            "Social media optimized graphic with viral-worthy visual elements and bright engaging colors, dynamic composition with the text \"{text}\" positioned for maximum impact, trendy professional design",
            "Current trend-inspired layout with bold geometric elements and modern color scheme, with the text \"{text}\" styled for viral potential, contemporary business aesthetic"
        ],
        # CLASSIC TEMPLATES - Professional abstract designs
        "Balance": [
            f"Sophisticated symmetrical composition with balanced geometric elements, refined color palette featuring {brand_color_description} with complementary neutrals, elegant typography, with the text \"{{text}}\" in premium corporate style, polished trustworthy aesthetic",
            f"Clean minimalist layout with harmonious design elements and calming color balance featuring {brand_color_description}, with the text \"{{text}}\" as central unifying element, professional reliable aesthetic",
            f"Premium corporate design with balanced visual hierarchy and sophisticated color scheme centered on {brand_color_description}, with the text \"{{text}}\" seamlessly integrated, credible business aesthetic"
        ],
        "Educational": [
            "Informative diagram layout with clear visual hierarchy and educational icons, learning-focused colors (blue, green, white), with the text \"{text}\" as main educational point, instructional professional aesthetic",
            "Knowledge-sharing infographic with clean academic styling and organized information flow, with the text \"{text}\" presented as valuable insight, educational trustworthy design",
            "Learning-focused layout with clear accessible design and instructional visual elements, with the text \"{text}\" as educational content, informative engaging aesthetic"
        ],
        "Motivational": [
            "Inspiring abstract composition with upward arrows and achievement symbols, energetic colors (bright blue, orange, yellow), with the text \"{text}\" as powerful motivational message, energizing positive aesthetic",
            "Success-oriented graphic with triumphant geometric elements and growth indicators, with the text \"{text}\" positioned as inspiring call-to-action, motivational empowering design",
            "Achievement-focused layout with upward movement patterns and success symbols, vibrant encouraging colors with the text \"{text}\" celebrating accomplishment, inspiring professional aesthetic"
        ]
    }

    # Select ONE style for the entire batch (consistency)
    import random
    prompts = detailed_prompts.get(template, detailed_prompts["Balance"])
    selected_style = random.choice(prompts)

    return selected_style


def create_smart_ideogram_prompt(text: str, platform: str, template: str, batch_style: str = None) -> str:
    """
    Create consistent prompts using the selected batch style.

    Args:
        text: Optimized text content
        platform: Target platform
        template: Content template type
        batch_style: Pre-selected style for the entire batch (for consistency)

    Returns:
        Consistent prompt using the batch style
    """
    if batch_style:
        # Use the pre-selected batch style for consistency
        return batch_style.format(text=text)
    else:
        # Fallback to random selection (shouldn't happen in batch generation)
        return select_consistent_style_for_batch(template, None).format(text=text)


def get_platform_character_limits(platform: str) -> Dict[str, int]:
    """
    Get optimal character limits for each social media platform.
    Based on best practices for engagement and readability.

    Args:
        platform: Target social media platform

    Returns:
        Dictionary with optimal and maximum character limits
    """
    limits = {
        "Instagram": {"optimal": 125, "maximum": 150},
        "LinkedIn": {"optimal": 150, "maximum": 200},
        "Facebook": {"optimal": 100, "maximum": 150},
        "X": {"optimal": 100, "maximum": 120},
        "Twitter": {"optimal": 100, "maximum": 120}  # Alias for X
    }

    return limits.get(platform, {"optimal": 125, "maximum": 150})


async def generate_strategic_post_content(content_strategy: Dict[str, Any], visual_hook: str, brand_info: Dict[str, Any], platform: str = "Instagram") -> str:
    """
    Generate concise, punchy social media content optimized for each platform.
    Creates professional content that complements (not duplicates) the visual hook.

    Args:
        content_strategy: Strategic content plan
        visual_hook: The text that appears in the image
        brand_info: Brand information
        platform: Target platform for character optimization

    Returns:
        Generated post content optimized for social media (concise and engaging)
    """
    business_name = brand_info.get("businessName", "Business")
    industry = brand_info.get("industry", "business")
    target_audience = brand_info.get("target_audience", "professionals")
    voice = brand_info.get("voice", "professional")

    content_type = content_strategy.get("content_type", "educational_professional")
    post_strategy = content_strategy.get("post_strategy", "")
    platform_adaptation = content_strategy.get("platform_adaptation", {})

    # Get platform-specific character limits
    char_limits = get_platform_character_limits(platform)
    optimal_chars = char_limits["optimal"]
    max_chars = char_limits["maximum"]

    # Create optimized prompt for CONCISE social media content
    ai_prompt = f"""Crea un post CONCISO y PUNCHY para {business_name} en {platform}.

LÍMITES ESTRICTOS:
- MÁXIMO {optimal_chars} caracteres (ideal para {platform})
- NUNCA exceder {max_chars} caracteres
- Incluir espacios, emojis y hashtags en el conteo

CONTEXTO:
- Negocio: {business_name}
- Industria: {industry}
- Plataforma: {platform}
- Audiencia: {target_audience}

GANCHO VISUAL (ya en imagen): "{visual_hook}"

FORMATO REQUERIDO PARA {platform.upper()}:
1. APERTURA impactante (1 línea)
2. INSIGHT valioso (1-2 líneas máximo)
3. CTA específico (1 línea)
4. 2-3 hashtags relevantes

REGLAS CRÍTICAS:
✅ CONCISO - Máximo {optimal_chars} caracteres
✅ PUNCHY - Cada palabra cuenta
✅ NO repetir el gancho visual
✅ COMPLEMENTAR la imagen con contexto
✅ PROFESIONAL pero conversacional
✅ CTA específico y claro
✅ Hashtags estratégicos

EJEMPLOS DE ESTRUCTURA:
"[Insight específico sobre el gancho visual]

[Contexto valioso en 1-2 líneas]

[CTA específico]

#hashtag1 #hashtag2 #hashtag3"

IMPORTANTE:
- Responde SOLO en español
- Cuenta caracteres estrictamente
- Optimiza para {platform}
- Máximo {optimal_chars} caracteres TOTAL"""

    try:
        # Use Gemini AI to generate the content
        import google.generativeai as genai
        from app.core.config import settings

        # Configure Gemini with API key
        if not settings.GEMINI_API_KEY:
            logger.error("GEMINI_API_KEY not configured")
            return generate_strategic_fallback_content(content_type, visual_hook, brand_info, platform)

        genai.configure(api_key=settings.GEMINI_API_KEY)
        model = genai.GenerativeModel('gemini-1.5-flash')
        response = model.generate_content(ai_prompt)

        if response and response.text:
            generated_content = response.text.strip()

            # Ensure the content doesn't duplicate the visual hook
            if visual_hook.lower() in generated_content.lower():
                # Remove any direct repetition of the visual hook
                generated_content = generated_content.replace(visual_hook, "").strip()
                # Clean up any resulting formatting issues
                generated_content = " ".join(generated_content.split())

            # Optimize content for platform character limits
            optimized_content = optimize_content_for_platform(generated_content, platform, char_limits)

            return optimized_content
        else:
            # Fallback to strategic template if AI fails
            return generate_strategic_fallback_content(content_type, visual_hook, brand_info, platform)

    except Exception as e:
        logger.error(f"Error generating AI content: {e}")
        # Fallback to strategic template
        return generate_strategic_fallback_content(content_type, visual_hook, brand_info, platform)


def optimize_content_for_platform(content: str, platform: str, char_limits: Dict[str, int]) -> str:
    """
    Optimize generated content to fit platform character limits while maintaining quality.

    Args:
        content: Generated content from AI
        platform: Target platform
        char_limits: Character limits for the platform

    Returns:
        Optimized content that fits platform requirements
    """
    optimal_chars = char_limits["optimal"]
    max_chars = char_limits["maximum"]

    # If content is already within optimal range, return as-is
    if len(content) <= optimal_chars:
        return content

    # If content exceeds maximum, we need to trim it intelligently
    if len(content) > max_chars:
        # Split into lines and prioritize most important parts
        lines = content.split('\n')
        optimized_lines = []
        current_length = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Prioritize lines with CTAs, insights, or hashtags
            if any(keyword in line.lower() for keyword in ['#', '¿', '?', '👉', '✅', '💡']):
                # Keep important lines
                if current_length + len(line) + 1 <= max_chars:
                    optimized_lines.append(line)
                    current_length += len(line) + 1
            elif current_length + len(line) + 1 <= optimal_chars:
                # Add other lines if within optimal range
                optimized_lines.append(line)
                current_length += len(line) + 1

        content = '\n'.join(optimized_lines)

    # Final trim if still too long
    if len(content) > max_chars:
        content = content[:max_chars-3] + "..."

    return content.strip()


def generate_strategic_fallback_content(content_type: str, visual_hook: str, brand_info: Dict[str, Any], platform: str = "Instagram") -> str:
    """
    Generate strategic fallback content when AI generation fails.
    Creates professional, concise content optimized for social media platforms.
    """
    business_name = brand_info.get("businessName", "Business")
    industry = brand_info.get("industry", "business")

    # Get platform character limits for fallback content
    char_limits = get_platform_character_limits(platform)
    optimal_chars = char_limits["optimal"]

    # Concise fallback templates optimized for social media
    fallback_templates = {
        "educational_professional": f"""La diferencia entre éxito y estancamiento en {industry}? La implementación consistente.

En {business_name} hemos desarrollado un proceso que genera resultados medibles.

¿Cuál resuena más contigo?

#{industry.replace(' ', '')} #estrategia #crecimiento""",

        "tutorial_educational": f"""Años perfeccionando este proceso confirman: estos pasos funcionan cuando se ejecutan correctamente.

Lo clave: no saltarse ningún paso y adaptar a tu negocio específico.

¿En qué paso necesitas profundizar?

#tutorial #{industry.replace(' ', '')} #aprendizaje""",

        "inspirational_story": f"""Esta transformación requirió dedicación, estrategia y perseverancia.

En {business_name} creemos que cada empresa puede lograr resultados extraordinarios. La diferencia: enfoque correcto + apoyo adecuado.

¿Qué paso darás hoy?

#inspiracion #{industry.replace(' ', '')} #exito""",

        "news_analysis": f"""Este desarrollo en {industry} tiene implicaciones importantes para el sector.

En {business_name} monitoreamos estos cambios y adaptamos estrategias para nuevas oportunidades.

¿Cómo impacta a tu empresa?

#tendencias #{industry.replace(' ', '')} #innovacion"""
    }

    selected_template = fallback_templates.get(content_type, fallback_templates["educational_professional"])

    # Optimize fallback content for platform
    char_limits = get_platform_character_limits(platform)
    optimized_fallback = optimize_content_for_platform(selected_template, platform, char_limits)

    return optimized_fallback


def generate_image_prompt(template: str, brand_info: Dict[str, Any], post_text: str, platform: str) -> str:
    """
    Legacy function for backward compatibility with the /generate endpoint.
    Creates basic image prompts - the new strategic system uses generate_image_prompt_strategic.
    """
    business_name = brand_info.get("businessName", "Business")
    industry = brand_info.get("industry", "business")

    # Basic image prompts for backward compatibility
    basic_prompts = {
        "Balance": f"Professional business graphic for {business_name} in {industry}, clean modern design, corporate aesthetic",
        "Educational": f"Educational infographic style for {business_name}, clean layout, professional {industry} context",
        "Motivational": f"Inspiring motivational design for {business_name}, uplifting colors, success-oriented imagery",
        "Informativo": f"Informational graphic for {business_name}, data visualization, {industry} professional design"
    }

    base_prompt = basic_prompts.get(template, basic_prompts["Balance"])

    # Add platform optimization
    platform_additions = {
        "Instagram": "square format, Instagram-optimized, social media aesthetic",
        "LinkedIn": "professional business setting, corporate aesthetic, LinkedIn-appropriate",
        "Facebook": "engaging social media style, Facebook-optimized, community feel",
        "X": "dynamic composition, Twitter-style visual, concise visual impact"
    }

    platform_addition = platform_additions.get(platform, platform_additions["Instagram"])

    return f"{base_prompt}, {platform_addition}"


def generate_post_text(template: str, brand_info: Dict[str, Any], index: int) -> str:
    """
    Legacy function maintained for backward compatibility.
    Generates basic post text - will be replaced by strategic content generation.
    """
    business_name = brand_info.get("businessName", "Business")
    industry = brand_info.get("industry", "business")

    # Simplified professional templates for backward compatibility
    basic_templates = {
        "Balance": [
            f"Estrategias clave para el éxito en {industry} que todo profesional debe conocer.",
            f"La importancia de la innovación constante en {industry} para mantenerse competitivo.",
            f"Cómo {business_name} está transformando el panorama de {industry}."
        ],
        "Educational": [
            f"Guía completa: Los fundamentos esenciales de {industry} explicados paso a paso.",
            f"Metodologías probadas que {business_name} utiliza para optimizar resultados en {industry}.",
            f"Tutorial: Cómo implementar las mejores prácticas de {industry} en tu negocio."
        ],
        "Motivational": [
            f"El camino hacia el éxito en {industry} comienza con una mentalidad de crecimiento.",
            f"Inspiración diaria: Cómo {business_name} supera los desafíos en {industry}.",
            f"Tu potencial en {industry} es ilimitado. Descubre cómo desbloquearlo."
        ]
    }

    templates = basic_templates.get(template, basic_templates["Balance"])
    return templates[(index - 1) % len(templates)]


@router.post("/generate-batch", response_model=PostGenerationResponse)
async def generate_posts_batch(request: PostGenerationRequest) -> PostGenerationResponse:
    """
    Generate social media posts efficiently using Ideogram's batch generation.

    This endpoint:
    1. Uses Gemini for text/copy generation
    2. Uses Ideogram 3.0 Quality batch generation for multiple images in one call
    3. Much faster and more efficient than individual calls
    """
    logger.info("🚀 Starting efficient batch post generation (fixed)")

    try:
        brand_info = request.brandInfo
        design_config = request.designConfig
        generation_config = request.generationConfig

        template = design_config.get("selectedTheme", "Balance")
        platform = design_config.get("platform", "Instagram")
        content_type = design_config.get("contentType", "instagram_posts")
        post_count = generation_config.get("count", 3)

        logger.info(f"📋 Generating {post_count} posts: template={template}, platform={platform}")

        # Select ONE consistent style for the entire batch with brand color
        brand_color = brand_info.get("brandColor") or brand_info.get("brand_color")
        batch_style = select_consistent_style_for_batch(template, brand_color)
        logger.info(f"🎨 Selected consistent style for batch: {batch_style[:100]}...")

        # Generate strategic content plan (replaces template_prompt)
        content_strategy = generate_strategic_content_plan(template, brand_info, platform)
        logger.info(f"📋 Content strategy: {content_strategy['content_type']}")

        # Get platform-specific dimensions
        image_dimensions = get_platform_dimensions(platform, content_type)
        logger.info(f"🖼️ Using platform-specific dimensions: {image_dimensions} for {platform}")

        # Generate all strategic content first
        post_contents = []
        visual_hooks = []
        for i in range(post_count):
            # Generate unique visual hook for each post
            visual_hook = generate_visual_hook_content(content_strategy, brand_info)
            visual_hooks.append(visual_hook)

            # Generate complementary post content
            try:
                post_content = await generate_strategic_post_content(content_strategy, visual_hook, brand_info, platform)
                post_contents.append(post_content)
            except Exception as e:
                logger.warning(f"Failed to generate strategic content for post {i+1}: {e}")
                # Fallback to basic content
                fallback_content = generate_strategic_fallback_content(
                    content_strategy.get("content_type", "educational_professional"),
                    visual_hook,
                    brand_info,
                    platform
                )
                post_contents.append(fallback_content)

        # Map platform dimensions to Ideogram format
        ideogram_size = "1024x1024"  # Default square
        if image_dimensions in ["1080x1080", "1024x1024"]:
            ideogram_size = "1024x1024"  # Square (Instagram)
        elif "1080x1920" in image_dimensions or "1024x1792" in image_dimensions:
            ideogram_size = "1024x1792"  # Portrait (Instagram Stories)
        elif any(dim in image_dimensions for dim in ["1200x630", "1200x627", "1200x675", "1792x1024"]):
            ideogram_size = "1792x1024"  # Landscape (Facebook, LinkedIn, X/Twitter)

        logger.info(f"🔄 Mapping {image_dimensions} → {ideogram_size} for Ideogram 3.0 Quality")

        # Generate strategic images with visual hooks
        image_urls = []
        for i, visual_hook in enumerate(visual_hooks):
            try:
                # Generate strategic image prompt with the visual hook and brand color
                brand_color = brand_info.get("brandColor") or brand_info.get("brand_color")
                strategic_image_prompt = generate_image_prompt_strategic(visual_hook, content_strategy, platform, brand_color)

                logger.info(f"🎨 Generating image {i+1} with hook: '{visual_hook[:50]}...'")

                ideogram_response = await ideogram_service.generate_ad(
                    prompt=strategic_image_prompt,
                    size=ideogram_size
                )

                if ideogram_response.get("success") and ideogram_response.get("image_url"):
                    image_urls.append(ideogram_response.get("image_url"))
                    logger.info(f"✅ Generated strategic image {i+1} successfully!")
                else:
                    logger.warning(f"🔄 Image {i+1} generation failed: {ideogram_response.get('error', 'Unknown error')}")
                    image_urls.append(None)

            except Exception as img_error:
                logger.warning(f"⚠️ Failed to generate strategic image {i+1}: {img_error}")
                image_urls.append(None)

        # Create strategic posts with separated content
        generated_posts = []
        for i in range(post_count):
            post = GeneratedPost(
                id=f"post_{i+1}_{template.lower()}",
                text=post_contents[i],  # Strategic post content
                image_url=image_urls[i] if i < len(image_urls) else None,
                template=template,
                platform=platform,
                metadata={
                    "visual_hook": visual_hooks[i],  # Text that appears in image
                    "content_strategy": content_strategy["content_type"],
                    "brand_name": brand_info.get("businessName", "Unknown"),
                    "generation_index": i+1,
                    "provider": "ideogram",
                    "image_generated": image_urls[i] is not None if i < len(image_urls) else False,
                    "dimensions": image_dimensions,
                    "content_type": content_type,
                    "batch_generated": True,
                    "generation_method": "strategic_separation",
                    "content_separation": "visual_hook_and_post_content_separated"
                }
            )
            generated_posts.append(post)

        return PostGenerationResponse(
            success=True,
            posts=generated_posts,
            total_generated=len(generated_posts),
            metadata={
                "template": template,
                "platform": platform,
                "content_type": content_type,
                "content_strategy": content_strategy["content_type"],
                "images_generated": sum(1 for post in generated_posts if post.image_url is not None),
                "provider": "ideogram",
                "dimensions": image_dimensions,
                "platform_optimized": True,
                "batch_generated": True,
                "generation_method": "strategic_separation",
                "content_quality": "professional_ai_generated",
                "visual_textual_separation": True
            }
        )

    except Exception as e:
        logger.error(f"❌ Error generating posts in batch: {e}")
        return PostGenerationResponse(
            success=False,
            error=f"Failed to generate posts: {str(e)}"
        )


@router.post("/generate", response_model=PostGenerationResponse)
async def generate_posts(request: PostGenerationRequest) -> PostGenerationResponse:
    """
    Generate social media posts using AI providers based on brand data and template selection.

    This endpoint:
    1. Uses Gemini for text/copy generation
    2. Uses Ideogram 3.0 Quality for all image generation (professional, text-heavy, visual content)
    3. Uses local meme service for template-based memes (cost optimization)
    4. Generates posts based on the selected template
    """
    logger.info("🚀 Starting post generation process")
    
    try:
        brand_info = request.brandInfo
        design_config = request.designConfig
        generation_config = request.generationConfig
        
        template = design_config.get("selectedTheme", "Balance")
        platform = design_config.get("platform", "Instagram")
        content_type = design_config.get("contentType", "instagram_posts")
        
        logger.info(f"📋 Generating posts: template={template}, platform={platform}")

        # Generate strategic content plan (replaces template_prompt)
        content_strategy = generate_strategic_content_plan(template, brand_info, platform)
        logger.info(f"📋 Content strategy: {content_strategy['content_type']}")

        # Generate the specified number of posts (default 3)
        post_count = generation_config.get("count", 3)

        # Get platform-specific dimensions
        image_dimensions = get_platform_dimensions(platform, content_type)
        logger.info(f"🖼️ Using platform-specific dimensions: {image_dimensions} for {platform}")

        # Generate real posts with images
        generated_posts = []
        for i in range(post_count):
            # Generate engaging social media copy (SEPARATE from image)
            post_text = generate_post_text(template, brand_info, i+1)

            # Generate SEPARATE visual description that complements the text
            image_prompt = generate_image_prompt(template, brand_info, post_text, platform)

            # Select image provider based on template and content type
            image_provider = select_image_provider(template, image_prompt)

            # Generate image based on provider
            image_url = None
            try:
                if image_provider == "meme":
                    # LOCAL MEME service for template-based memes (cost optimization)
                    meme_result = await meme_service.generate_meme(post_text)
                    if meme_result and meme_result.get("success"):
                        image_url = meme_result.get("image_url")
                        image_provider = "meme"  # Confirm provider for metadata

                elif image_provider == "ideogram":
                    # IDEOGRAM 3.0 QUALITY for all content types (text-heavy, visual, professional)
                    # Map platform dimensions to supported Ideogram sizes
                    ideogram_size = "1024x1024"  # Default fallback

                    if image_dimensions in ["1080x1080", "1024x1024"]:
                        ideogram_size = "1024x1024"  # Square (Instagram)
                    elif "1080x1920" in image_dimensions or "1024x1792" in image_dimensions:
                        ideogram_size = "1024x1792"  # Portrait (Instagram Stories)
                    elif any(dim in image_dimensions for dim in ["1200x630", "1200x627", "1200x675", "1792x1024"]):
                        ideogram_size = "1792x1024"  # Landscape (Facebook, LinkedIn, X/Twitter)

                    logger.info(f"🔄 Mapping {image_dimensions} → {ideogram_size} for Ideogram 3.0 Quality")

                    # Use Ideogram 3.0 Quality for professional image generation
                    ideogram_response = await ideogram_service.generate_ad(
                        prompt=f"Professional social media content: {image_prompt}. High-quality design for {platform}. Modern, engaging, professional aesthetic.",
                        size=ideogram_size
                    )

                    if ideogram_response.get("success"):
                        image_url = ideogram_response.get("image_url")
                    else:
                        logger.warning(f"🔄 Ideogram failed: {ideogram_response.get('error', 'Unknown error')}")

                logger.info(f"✅ Generated image for post {i+1} using {image_provider}: {image_url is not None}")

            except Exception as img_error:
                logger.warning(f"⚠️ Failed to generate image for post {i+1} with {image_provider}: {img_error}")
                # Continue without image if generation fails

            post = GeneratedPost(
                id=f"post_{i+1}_{template.lower()}",
                text=post_text,
                image_url=image_url,
                template=template,
                platform=platform,
                metadata={
                    "content_strategy": content_strategy["content_type"],
                    "image_prompt": image_prompt,  # Include separate image prompt for debugging
                    "brand_name": brand_info.get("businessName", "Unknown"),
                    "generation_index": i+1,
                    "provider": image_provider,
                    "image_generated": image_url is not None,
                    "dimensions": image_dimensions,
                    "content_type": content_type,
                    "content_separation": "strategic_content_generation",  # Flag for new approach
                    "generation_method": "strategic_separation"
                }
            )
            generated_posts.append(post)
        
        return PostGenerationResponse(
            success=True,
            posts=generated_posts,
            total_generated=len(generated_posts),
            metadata={
                "template": template,
                "platform": platform,
                "content_type": content_type,
                "content_strategy": content_strategy["content_type"],
                "images_generated": sum(1 for post in generated_posts if post.image_url is not None),
                "providers_used": list(set(post.metadata.get("provider") for post in generated_posts)),
                "dimensions": image_dimensions,
                "platform_optimized": True,
                "generation_method": "strategic_separation"
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Error generating posts: {e}")
        return PostGenerationResponse(
            success=False,
            error=f"Failed to generate posts: {str(e)}"
        )


class MemeGenerationRequest(BaseModel):
    """Request schema for meme generation test."""
    prompt: str = Field(..., description="Meme text/prompt")
    business_name: str = Field(default="Business", description="Name of the business")
    industry: str = Field(default="general", description="Industry type")

@router.post("/generate-meme")
async def generate_meme_endpoint(request: MemeGenerationRequest) -> Dict[str, Any]:
    """
    Test endpoint for meme generation using hybrid approach.

    Args:
        request: Meme generation request

    Returns:
        Generated meme result
    """
    logger.info(f"🎭 Testing meme generation: {request.prompt}")

    try:
        # Create mock brand info
        brand_info = {
            "businessName": request.business_name,
            "brandAnalysis": {
                "industry": request.industry
            }
        }

        # Generate meme
        meme_result = await meme_service.generate_meme(request.prompt)

        if meme_result and meme_result.get("success"):
            return {
                "success": True,
                "meme_url": meme_result.get("image_url"),
                "prompt": request.prompt,
                "business_name": request.business_name,
                "message": "Meme generated successfully!",
                "metadata": meme_result.get("metadata", {})
            }
        else:
            return {
                "success": False,
                "error": meme_result.get("error", "Failed to generate meme"),
                "prompt": request.prompt
            }

    except Exception as e:
        logger.error(f"❌ Error in meme generation test: {e}")
        return {
            "success": False,
            "error": f"Error: {str(e)}",
            "prompt": request.prompt
        }
