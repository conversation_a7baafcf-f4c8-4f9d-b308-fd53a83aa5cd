"use client"
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import { useState, useEffect, useRef } from "react";
import { <PERSON>R<PERSON>, <PERSON>Left, <PERSON>ap, Heart, Star } from "lucide-react";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

// Componentes refactorizados
import { HeroSection } from "@/components/dashboard/HeroSection";
import { WorkflowSection } from "@/components/dashboard/WorkflowSection";
import { EmmaSuggestions } from "@/components/dashboard/EmmaSuggestions";
import { ActivitySection } from "@/components/dashboard/ActivitySection";

// Datos
import { quickActions, workflowSections } from "@/data/dashboardData";



// Componente QuickActionsCarousel con scroll horizontal deslizable
function QuickActionsCarousel({ quickActions, onNavigate }: {
  quickActions: any[],
  onNavigate: (path: string) => void
}) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!scrollContainerRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - scrollContainerRef.current.offsetLeft);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
    scrollContainerRef.current.style.cursor = 'grabbing';
    scrollContainerRef.current.style.userSelect = 'none';
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return;
    e.preventDefault();
    const x = e.pageX - scrollContainerRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.cursor = 'grab';
      scrollContainerRef.current.style.userSelect = 'auto';
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.cursor = 'grab';
      scrollContainerRef.current.style.userSelect = 'auto';
    }
  };

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1.0 }}
      className="py-16"
    >
      <div className="container mx-auto">
        <div className="mb-8 flex items-end justify-between md:mb-14 lg:mb-16">
          <div className="flex flex-col gap-4">
            <h2 className="text-3xl font-bold md:text-4xl lg:text-5xl bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              Acciones Rápidas
            </h2>
            <p className="max-w-lg text-gray-600 text-lg">
              Tareas más comunes para empezar ahora mismo • Desliza para ver más
            </p>
          </div>
        </div>
      </div>
      <div className="w-full">
        <div
          ref={scrollContainerRef}
          className="flex gap-5 overflow-x-auto no-scrollbar cursor-grab select-none px-4 md:px-8 pb-4"
          style={{ scrollBehavior: 'smooth' }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
        >
          {quickActions.map((action, index) => (
            <div key={index} className="flex-shrink-0 w-[320px] lg:w-[360px]">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.1 + index * 0.1 }}
                className="group cursor-pointer rounded-2xl"
                onClick={(e) => {
                  if (!isDragging) {
                    onNavigate(action.path);
                  }
                }}
                whileHover={{
                  y: -8,
                  scale: 1.02,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
              >
                <div className="group relative h-full min-h-[20rem] max-w-full overflow-hidden rounded-2xl bg-white/90 backdrop-blur-md border border-white/20 hover:border-white/40 shadow-xl hover:shadow-2xl transition-all duration-500">
                  <div className="absolute top-0 left-0 right-0 h-32 opacity-90 bg-gradient-to-br from-purple-500 to-pink-500">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20"></div>
                    <div className="absolute right-0 top-0 w-24 h-24 bg-white/10 rounded-bl-[2rem] backdrop-blur-sm"></div>
                    <div className="absolute left-0 bottom-0 w-12 h-12 bg-white/15 rounded-tr-[1rem]"></div>
                  </div>
                  <div className="relative z-10 flex flex-col justify-center items-center p-8 pt-16">
                    <motion.div
                      className="w-20 h-20 bg-white/90 backdrop-blur-md rounded-2xl flex items-center justify-center mb-6 border border-white/30 shadow-xl"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <span className="text-3xl">{action.preview}</span>
                    </motion.div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 text-center group-hover:text-gray-700 transition-colors">
                      {action.title}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed text-center mb-6">
                      {action.description}
                    </p>
                    <div className="flex items-center text-sm font-semibold text-gray-500 group-hover:text-gray-700 transition-colors">
                      Empezar <ArrowRight className="ml-2 size-4 transition-transform group-hover:translate-x-1" />
                    </div>
                  </div>
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-transparent via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </div>
              </motion.div>
            </div>
          ))}
        </div>
      </div>
    </motion.section>
  );
}

function DashboardContent() {
  const [, navigate] = useLocation();

  return (
    <div className="space-y-12">
      <HeroSection />

      <QuickActionsCarousel
        quickActions={quickActions}
        onNavigate={navigate}
      />

      <WorkflowSection workflowSections={workflowSections} />

      <EmmaSuggestions />

      <ActivitySection />
    </div>
  );
}

function DashboardElegant() {
  return (
    <DashboardLayout pageTitle="Dashboard">
      <DashboardContent />
    </DashboardLayout>
  );
}

export default DashboardElegant;
